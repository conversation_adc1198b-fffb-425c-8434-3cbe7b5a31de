<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_campaign_comission_changes', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('acs_campaign_com_id')->constrained('acs_campaign_commission')->onDelete('cascade');
            $table->integer('variety_id');
            $table->integer('design_id');
            $table->integer('min_qty');
            $table->integer('max_qty');
            $table->decimal('comission_percentage', 10, 2);
            $table->integer('status')->default(0);
            $table->foreignUlid('action_by')->constrained('acs_users');
            $table->foreignUlid('aprove_by')->constrained('acs_users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_campaign_comission_changes');
    }
};
