<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_terms_conditions_acceptance', function (Blueprint $table) {
            $table->id();
            $table->foreignId('acs_role_id')->constrained('acs_roles')->onDelete('cascade');
            $table->foreignUlid('acs_user_id')->constrained('acs_users')->onDelete('cascade');
            $table->timestamp('accept_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_terms_conditions_acceptance');
    }
};
