<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the super_admin role ID
        $superAdminRole = DB::table('acs_roles')
                           ->where('name', 'super_admin')
                           ->first();

        if (!$superAdminRole) {
            $this->command->error('Super admin role not found. Please run AcsRoleSeeder first.');
            return;
        }

        // Create super admin user
        $superAdminData = [
            'id' => (string) Str::ulid(),
            'name' => 'Super Admin',
            'acs_role_id' => $superAdminRole->id,
            'username' => 'super_admin',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => Hash::make('123'),
            'acs_coorperative_id' => null,
            'acs_coorperative_branch_id' => null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];

        // Insert super admin user if they don't exist
        DB::table('acs_users')->updateOrInsert(
            ['email' => $superAdminData['email']],
            $superAdminData
        );

        $this->command->info('Super admin user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: 123');
    }
}
