<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the admin role ID
        $adminRole = DB::table('acs_roles')
                      ->where('name', 'admin')
                      ->first();

        if (!$adminRole) {
            $this->command->error('Admin role not found. Please run AcsRoleSeeder first.');
            return;
        }

        // Create admin user
        $adminData = [
            'id' => (string) Str::ulid(),
            'name' => 'Admin User',
            'acs_role_id' => $adminRole->id,
            'username' => 'admin',
            'email' => '<EMAIL>',
            'phone' => '1234567891',
            'password' => Hash::make('123'),
            'acs_coorperative_id' => null,
            'acs_coorperative_branch_id' => null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];

        // Insert admin user if they don't exist
        DB::table('acs_users')->updateOrInsert(
            ['email' => $adminData['email']],
            $adminData
        );

        $this->command->info('Admin user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: 123');
    }
}
