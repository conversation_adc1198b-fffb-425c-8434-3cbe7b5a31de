<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;

class TestUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get role IDs
        $masterAgentRole = DB::table('acs_roles')->where('name', 'master_agent')->first();
        $agentRole = DB::table('acs_roles')->where('name', 'agent')->first();

        if (!$masterAgentRole || !$agentRole) {
            $this->command->error('Required roles not found. Please run AcsRoleSeeder first.');
            return;
        }

        // Create Master Agent user
        $masterAgentData = [
            'id' => (string) Str::ulid(),
            'name' => 'Master Agent User',
            'acs_role_id' => $masterAgentRole->id,
            'username' => 'master_agent',
            'email' => '<EMAIL>',
            'phone' => '1234567892',
            'password' => Hash::make('123'),
            'acs_coorperative_id' => null,
            'acs_coorperative_branch_id' => null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];

        // Create Agent user
        $agentData = [
            'id' => (string) Str::ulid(),
            'name' => 'Agent User',
            'acs_role_id' => $agentRole->id,
            'username' => 'agent',
            'email' => '<EMAIL>',
            'phone' => '1234567893',
            'password' => Hash::make('123'),
            'acs_coorperative_id' => null,
            'acs_coorperative_branch_id' => null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];

        // Insert users if they don't exist
        DB::table('acs_users')->updateOrInsert(
            ['email' => $masterAgentData['email']],
            $masterAgentData
        );

        DB::table('acs_users')->updateOrInsert(
            ['email' => $agentData['email']],
            $agentData
        );

        $this->command->info('Test users created successfully!');
        $this->command->info('Master Agent - Email: <EMAIL>, Password: 123');
        $this->command->info('Agent - Email: <EMAIL>, Password: 123');
    }
}
