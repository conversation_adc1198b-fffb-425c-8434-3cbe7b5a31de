<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AcsCampaignCommissionService extends BaseService
{
    public function findAll()
    {
        DB::beginTransaction();
        try {
            $result = DB::table('acs_campaign_commission')
                        ->whereNull('deleted_at')
                        ->get();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function findOne($id)
    {
        DB::beginTransaction();
        try {
            $result = DB::table('acs_campaign_commission')
                        ->where('id', $id)
                        ->whereNull('deleted_at')
                        ->first();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_campaign_commission')->insert([
                'acs_campaign_id' => $request->acs_campaign_id ?? null,
                'variety_id' => $request->variety_id ?? null,
                'design_id' => $request->design_id ?? null,
                'min_qty' => $request->min_qty ?? null,
                'max_qty' => $request->max_qty ?? null,
                'comission_percentage' => $request->comission_percentage ?? null,
                'action_by' => $request->action_by ?? null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_campaign_commission')
                ->where('id', $id)
                ->update([
                    'acs_campaign_id' => $request->acs_campaign_id ?? null,
                'variety_id' => $request->variety_id ?? null,
                'design_id' => $request->design_id ?? null,
                'min_qty' => $request->min_qty ?? null,
                'max_qty' => $request->max_qty ?? null,
                'comission_percentage' => $request->comission_percentage ?? null,
                'action_by' => $request->action_by ?? null,
                    'updated_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_campaign_commission')
                ->where('id', $id)
                ->update([
                    'deleted_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }
}