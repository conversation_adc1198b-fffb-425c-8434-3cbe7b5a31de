<?php

namespace App\Services;

use App\Models\AcsUser;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AuthService extends BaseService
{
    /**
     * Attempt to login with email/username and password
     */
    public function login(Request $request)
    {
        DB::beginTransaction();
        try {
            $credentials = $this->getCredentials($request);

            if (!$credentials) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Invalid credentials provided'
                ];
            }

            $user = $this->findUserByCredentials($credentials);

            if (!$user) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'User not found'
                ];
            }

            if (!$this->verifyPassword($credentials['password'], $user->password)) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Invalid password'
                ];
            }

            // Login the user
            Auth::login($user, $request->remember ?? false);

            DB::commit();
            return [
                'success' => true,
                'message' => 'Login successful',
                'user' => $user,
                'redirect_url' => $this->getRedirectUrl($user)
            ];
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return [
                'success' => false,
                'message' => 'An error occurred during login'
            ];
        }
    }

    /**
     * Logout the current user
     */
    public function logout()
    {
        try {
            Auth::logout();
            return [
                'success' => true,
                'message' => 'Logout successful'
            ];
        } catch (\Throwable $e) {
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return [
                'success' => false,
                'message' => 'An error occurred during logout'
            ];
        }
    }

    /**
     * Get credentials from request
     */
    private function getCredentials(Request $request)
    {
        $login = $request->email ?? $request->username;
        $password = $request->password;

        if (!$login || !$password) {
            return null;
        }

        return [
            'login' => $login,
            'password' => $password
        ];
    }

    /**
     * Find user by email or username
     */
    private function findUserByCredentials($credentials)
    {
        return AcsUser::where('email', $credentials['login'])
                     ->orWhere('username', $credentials['login'])
                     ->whereNull('deleted_at')
                     ->with('role')
                     ->first();
    }

    /**
     * Verify password
     */
    private function verifyPassword($inputPassword, $hashedPassword)
    {
        return Hash::check($inputPassword, $hashedPassword);
    }

    /**
     * Get redirect URL based on user role ID
     */
    private function getRedirectUrl($user)
    {
        if (!$user->role) {
            return url('/');
        }

        switch ($user->role->id) {
            case 1: // super_admin
                return url('/super-admin/dashboard');
            case 2: // admin
                return url('/admin/dashboard');
            case 3: // master_agent
                return url('/master-agent/dashboard');
            case 4: // agent
                return url('/agent/dashboard');
            default:
                return url('/');
        }
    }

    /**
     * Check if user is authenticated
     */
    public function isAuthenticated()
    {
        return Auth::check();
    }

    /**
     * Get current authenticated user
     */
    public function getCurrentUser()
    {
        return Auth::user();
    }

    /**
     * Find user by ID
     */
    public function findUserById($id)
    {
        DB::beginTransaction();
        try {
            $result = AcsUser::where('id', $id)
                            ->whereNull('deleted_at')
                            ->with('role')
                            ->first();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }
}
