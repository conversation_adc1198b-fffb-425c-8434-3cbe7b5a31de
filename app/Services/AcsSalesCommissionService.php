<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AcsSalesCommissionService extends BaseService
{
    public function findAll()
    {
        DB::beginTransaction();
        try {
            $result = DB::table('acs_sales_commission')
                        ->whereNull('deleted_at')
                        ->get();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function findOne($id)
    {
        DB::beginTransaction();
        try {
            $result = DB::table('acs_sales_commission')
                        ->where('id', $id)
                        ->whereNull('deleted_at')
                        ->first();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_sales_commission')->insert([
                'table_22_jualan_id' => $request->table_22_jualan_id ?? null,
                'table_23_senarai_jualan_id' => $request->table_23_senarai_jualan_id ?? null,
                'invoice_no' => $request->invoice_no ?? null,
                'acs_campaign_id' => $request->acs_campaign_id ?? null,
                'affiliate_membership_no' => $request->affiliate_membership_no ?? null,
                'senarai_pelanggan_id' => $request->senarai_pelanggan_id ?? null,
                'comission_percentage' => $request->comission_percentage ?? null,
                'comission_amount' => $request->comission_amount ?? null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_sales_commission')
                ->where('id', $id)
                ->update([
                    'table_22_jualan_id' => $request->table_22_jualan_id ?? null,
                'table_23_senarai_jualan_id' => $request->table_23_senarai_jualan_id ?? null,
                'invoice_no' => $request->invoice_no ?? null,
                'acs_campaign_id' => $request->acs_campaign_id ?? null,
                'affiliate_membership_no' => $request->affiliate_membership_no ?? null,
                'senarai_pelanggan_id' => $request->senarai_pelanggan_id ?? null,
                'comission_percentage' => $request->comission_percentage ?? null,
                'comission_amount' => $request->comission_amount ?? null,
                    'updated_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_sales_commission')
                ->where('id', $id)
                ->update([
                    'deleted_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }
}