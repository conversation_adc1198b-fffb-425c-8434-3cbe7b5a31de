<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class JobBatchService extends BaseService
{
    public function findAll()
    {
        DB::beginTransaction();
        try {
            $result = DB::table('job_batches')
                        ->whereNull('deleted_at')
                        ->get();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function findOne($id)
    {
        DB::beginTransaction();
        try {
            $result = DB::table('job_batches')
                        ->where('id', $id)
                        ->whereNull('deleted_at')
                        ->first();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            DB::table('job_batches')->insert([
                'name' => $request->name ?? null,
                'total_jobs' => $request->total_jobs ?? null,
                'pending_jobs' => $request->pending_jobs ?? null,
                'failed_jobs' => $request->failed_jobs ?? null,
                'failed_job_ids' => $request->failed_job_ids ?? null,
                'options' => $request->options ?? null,
                'cancelled_at' => $request->cancelled_at ?? null,
                'finished_at' => $request->finished_at ?? null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            DB::table('job_batches')
                ->where('id', $id)
                ->update([
                    'name' => $request->name ?? null,
                'total_jobs' => $request->total_jobs ?? null,
                'pending_jobs' => $request->pending_jobs ?? null,
                'failed_jobs' => $request->failed_jobs ?? null,
                'failed_job_ids' => $request->failed_job_ids ?? null,
                'options' => $request->options ?? null,
                'cancelled_at' => $request->cancelled_at ?? null,
                'finished_at' => $request->finished_at ?? null,
                    'updated_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            DB::table('job_batches')
                ->where('id', $id)
                ->update([
                    'deleted_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }
}