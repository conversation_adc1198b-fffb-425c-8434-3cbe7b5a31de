<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AcsCommissionDistributionService extends BaseService
{
    public function findAll()
    {
        DB::beginTransaction();
        try {
            $result = DB::table('acs_commission_distributions')
                        ->whereNull('deleted_at')
                        ->get();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function findOne($id)
    {
        DB::beginTransaction();
        try {
            $result = DB::table('acs_commission_distributions')
                        ->where('id', $id)
                        ->whereNull('deleted_at')
                        ->first();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_commission_distributions')->insert([
                'acs_sales_commission_id' => $request->acs_sales_commission_id ?? null,
                'acs_coorperative_id' => $request->acs_coorperative_id ?? null,
                'acs_coorperative_branch_id' => $request->acs_coorperative_branch_id ?? null,
                'acs_main_agent_user_id' => $request->acs_main_agent_user_id ?? null,
                'main_agent_percentage' => $request->main_agent_percentage ?? null,
                'main_agent_commission_amount' => $request->main_agent_commission_amount ?? null,
                'agent_percentage' => $request->agent_percentage ?? null,
                'agent_commission_amount' => $request->agent_commission_amount ?? null,
                'acs_agent_user_id' => $request->acs_agent_user_id ?? null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_commission_distributions')
                ->where('id', $id)
                ->update([
                    'acs_sales_commission_id' => $request->acs_sales_commission_id ?? null,
                'acs_coorperative_id' => $request->acs_coorperative_id ?? null,
                'acs_coorperative_branch_id' => $request->acs_coorperative_branch_id ?? null,
                'acs_main_agent_user_id' => $request->acs_main_agent_user_id ?? null,
                'main_agent_percentage' => $request->main_agent_percentage ?? null,
                'main_agent_commission_amount' => $request->main_agent_commission_amount ?? null,
                'agent_percentage' => $request->agent_percentage ?? null,
                'agent_commission_amount' => $request->agent_commission_amount ?? null,
                'acs_agent_user_id' => $request->acs_agent_user_id ?? null,
                    'updated_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_commission_distributions')
                ->where('id', $id)
                ->update([
                    'deleted_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }
}