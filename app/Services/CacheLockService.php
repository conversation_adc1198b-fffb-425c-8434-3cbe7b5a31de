<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class CacheLockService extends BaseService
{
    public function findAll()
    {
        DB::beginTransaction();
        try {
            $result = DB::table('cache_locks')
                        ->whereNull('deleted_at')
                        ->get();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function findOne($id)
    {
        DB::beginTransaction();
        try {
            $result = DB::table('cache_locks')
                        ->where('id', $id)
                        ->whereNull('deleted_at')
                        ->first();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            DB::table('cache_locks')->insert([
                'key' => $request->key ?? null,
                'owner' => $request->owner ?? null,
                'expiration' => $request->expiration ?? null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            DB::table('cache_locks')
                ->where('id', $id)
                ->update([
                    'key' => $request->key ?? null,
                'owner' => $request->owner ?? null,
                'expiration' => $request->expiration ?? null,
                    'updated_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            DB::table('cache_locks')
                ->where('id', $id)
                ->update([
                    'deleted_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }
}