<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class UserService extends BaseService
{
    public function findAll()
    {
        DB::beginTransaction();
        try {
            $result = DB::table('users')
                        ->whereNull('deleted_at')
                        ->get();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function findOne($id)
    {
        DB::beginTransaction();
        try {
            $result = DB::table('users')
                        ->where('id', $id)
                        ->whereNull('deleted_at')
                        ->first();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            DB::table('users')->insert([
                'name' => $request->name ?? null,
                'email' => $request->email ?? null,
                'email_verified_at' => $request->email_verified_at ?? null,
                'password' => $request->password ?? null,
                'remember_token' => $request->remember_token ?? null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            DB::table('users')
                ->where('id', $id)
                ->update([
                    'name' => $request->name ?? null,
                'email' => $request->email ?? null,
                'email_verified_at' => $request->email_verified_at ?? null,
                'password' => $request->password ?? null,
                'remember_token' => $request->remember_token ?? null,
                    'updated_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            DB::table('users')
                ->where('id', $id)
                ->update([
                    'deleted_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }
}