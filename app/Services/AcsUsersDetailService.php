<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AcsUsersDetailService extends BaseService
{
    public function findAll()
    {
        DB::beginTransaction();
        try {
            $result = DB::table('acs_users_details')
                        ->whereNull('deleted_at')
                        ->get();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function findOne($id)
    {
        DB::beginTransaction();
        try {
            $result = DB::table('acs_users_details')
                        ->where('id', $id)
                        ->whereNull('deleted_at')
                        ->first();
            DB::commit();
            return $result;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_users_details')->insert([
                'acs_users_id' => $request->acs_users_id ?? null,
                'no_ic' => $request->no_ic ?? null,
                'address_1' => $request->address_1 ?? null,
                'address_2' => $request->address_2 ?? null,
                'postcode' => $request->postcode ?? null,
                'city' => $request->city ?? null,
                'state' => $request->state ?? null,
                'working_position' => $request->working_position ?? null,
                'acs_range_income_id' => $request->acs_range_income_id ?? null,
                'bank_account_number' => $request->bank_account_number ?? null,
                'bank_account_name' => $request->bank_account_name ?? null,
                'bank_statement' => $request->bank_statement ?? null,
                'next_to_kin_name' => $request->next_to_kin_name ?? null,
                'next_to_kin_ic' => $request->next_to_kin_ic ?? null,
                'next_to_kin_phone' => $request->next_to_kin_phone ?? null,
                'kyc_approve_at' => $request->kyc_approve_at ?? null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_users_details')
                ->where('id', $id)
                ->update([
                    'acs_users_id' => $request->acs_users_id ?? null,
                'no_ic' => $request->no_ic ?? null,
                'address_1' => $request->address_1 ?? null,
                'address_2' => $request->address_2 ?? null,
                'postcode' => $request->postcode ?? null,
                'city' => $request->city ?? null,
                'state' => $request->state ?? null,
                'working_position' => $request->working_position ?? null,
                'acs_range_income_id' => $request->acs_range_income_id ?? null,
                'bank_account_number' => $request->bank_account_number ?? null,
                'bank_account_name' => $request->bank_account_name ?? null,
                'bank_statement' => $request->bank_statement ?? null,
                'next_to_kin_name' => $request->next_to_kin_name ?? null,
                'next_to_kin_ic' => $request->next_to_kin_ic ?? null,
                'next_to_kin_phone' => $request->next_to_kin_phone ?? null,
                'kyc_approve_at' => $request->kyc_approve_at ?? null,
                    'updated_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            DB::table('acs_users_details')
                ->where('id', $id)
                ->update([
                    'deleted_at' => Carbon::now(),
                ]);
            DB::commit();
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->errorLog(class_basename(self::class), __FUNCTION__, $e->getMessage(), $e->getLine());
            return false;
        }
    }
}