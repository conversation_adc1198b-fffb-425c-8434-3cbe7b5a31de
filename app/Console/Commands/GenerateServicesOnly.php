<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class GenerateServicesOnly extends Command
{
    protected $signature = 'generate:services';
    protected $description = 'Generate service files only based on all DB tables';

    public function handle()
    {
        $database = DB::getDatabaseName();
        $tables = DB::table('information_schema.tables')
            ->where('table_schema', $database)
            ->pluck('table_name')
            ->toArray();
        $serviceDir = app_path("Services");

        if (!file_exists($serviceDir)) {
            mkdir($serviceDir, 0755, true); // true = recursive, in case nested
        }

        foreach ($tables as $table) {
            $className = Str::studly(Str::singular($table));
            $serviceName = "{$className}Service";
            $servicePath = app_path("Services/{$serviceName}.php");

            if (!file_exists($servicePath)) {
                file_put_contents($servicePath, $this->generateServiceStub($table, $className));
                $this->info("Generated: {$serviceName}");
            } else {
                $this->warn("Skipped (exists): {$serviceName}");
            }
        }

        return 0;
    }

    protected function generateServiceStub($table, $className)
    {
        $columns = Schema::getColumnListing($table);

        // Remove default or non-editable columns
        $excluded = ['id', 'created_at', 'updated_at', 'deleted_at'];
        $fillable = array_diff($columns, $excluded);

        // Generate insert/update field array
        $fields = collect($fillable)->map(function ($column) {
            return "'$column' => \$request->$column ?? null,";
        })->implode(PHP_EOL . str_repeat(' ', 16));

        return <<<PHP
    <?php
    
    namespace App\Services;
    
    use Illuminate\Support\Facades\DB;
    use Illuminate\Http\Request;
    use Carbon\Carbon;
    
    class {$className}Service extends BaseService
    {
        public function findAll()
        {
            DB::beginTransaction();
            try {
                \$result = DB::table('{$table}')
                            ->whereNull('deleted_at')
                            ->get();
                DB::commit();
                return \$result;
            } catch (\Throwable \$e) {
                DB::rollBack();
                \$this->errorLog(class_basename(self::class), __FUNCTION__, \$e->getMessage(), \$e->getLine());
                return false;
            }
        }
    
        public function findOne(\$id)
        {
            DB::beginTransaction();
            try {
                \$result = DB::table('{$table}')
                            ->where('id', \$id)
                            ->whereNull('deleted_at')
                            ->first();
                DB::commit();
                return \$result;
            } catch (\Throwable \$e) {
                DB::rollBack();
                \$this->errorLog(class_basename(self::class), __FUNCTION__, \$e->getMessage(), \$e->getLine());
                return false;
            }
        }
    
        public function store(Request \$request)
        {
            DB::beginTransaction();
            try {
                DB::table('{$table}')->insert([
                    {$fields}
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
                DB::commit();
                return true;
            } catch (\Throwable \$e) {
                DB::rollBack();
                \$this->errorLog(class_basename(self::class), __FUNCTION__, \$e->getMessage(), \$e->getLine());
                return false;
            }
        }
    
        public function update(Request \$request, \$id)
        {
            DB::beginTransaction();
            try {
                DB::table('{$table}')
                    ->where('id', \$id)
                    ->update([
                        {$fields}
                        'updated_at' => Carbon::now(),
                    ]);
                DB::commit();
                return true;
            } catch (\Throwable \$e) {
                DB::rollBack();
                \$this->errorLog(class_basename(self::class), __FUNCTION__, \$e->getMessage(), \$e->getLine());
                return false;
            }
        }
    
        public function delete(\$id)
        {
            DB::beginTransaction();
            try {
                DB::table('{$table}')
                    ->where('id', \$id)
                    ->update([
                        'deleted_at' => Carbon::now(),
                    ]);
                DB::commit();
                return true;
            } catch (\Throwable \$e) {
                DB::rollBack();
                \$this->errorLog(class_basename(self::class), __FUNCTION__, \$e->getMessage(), \$e->getLine());
                return false;
            }
        }
    }
    PHP;
    }
}
