<?php

namespace App\Traits;


use Illuminate\Support\Facades\Log;

trait ErrorTrait
{

    protected function errorLog($class_name, $function_name, $message, $line)
    {
        Log::info("Log Error Detail\nClass Name : " . $class_name . "\nFunction : " . $function_name . "\nMessage : " . $message . "\nLine : " . $line);
        $request_url = request()->fullUrl();


        $error_description = "Log Error Detail\nUrl : $request_url \nClass Name : " . $class_name . "\nFunction : " . $function_name . "\nMessage : " . $message . "\nLine : " . $line;
    }
}
