<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AcsCoorperative extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'acs_coorperative';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'outlet_name',
    ];

    /**
     * Get the branches for the cooperative.
     */
    public function branches()
    {
        return $this->hasMany(AcsCoorperativeBranch::class, 'acs_coorperative_id');
    }

    /**
     * Get the users for the cooperative.
     */
    public function users()
    {
        return $this->hasMany(AcsUser::class, 'acs_coorperative_id');
    }
}
