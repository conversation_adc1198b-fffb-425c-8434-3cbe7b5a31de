<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class AcsUser extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes, HasUlids;

    protected $table = 'acs_users';
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'acs_role_id',
        'username',
        'email',
        'phone',
        'password',
        'acs_coorperative_id',
        'acs_coorperative_branch_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the role that owns the user.
     */
    public function role()
    {
        return $this->belongsTo(AcsRole::class, 'acs_role_id');
    }

    /**
     * Get the cooperative that owns the user.
     */
    public function cooperative()
    {
        return $this->belongsTo(AcsCoorperative::class, 'acs_coorperative_id');
    }

    /**
     * Get the cooperative branch that owns the user.
     */
    public function cooperativeBranch()
    {
        return $this->belongsTo(AcsCoorperativeBranch::class, 'acs_coorperative_branch_id');
    }

    /**
     * Get the user's role name.
     */
    public function getRoleNameAttribute()
    {
        return $this->role ? $this->role->name : null;
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole($roleName)
    {
        return $this->role && $this->role->name === $roleName;
    }

    /**
     * Check if user is super admin.
     */
    public function isSuperAdmin()
    {
        return $this->hasRole('super_admin');
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin()
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is master agent.
     */
    public function isMasterAgent()
    {
        return $this->hasRole('master_agent');
    }

    /**
     * Check if user is agent.
     */
    public function isAgent()
    {
        return $this->hasRole('agent');
    }
}
