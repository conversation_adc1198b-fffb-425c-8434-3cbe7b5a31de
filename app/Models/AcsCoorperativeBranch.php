<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AcsCoorperativeBranch extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'acs_coorperative_branch';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'acs_coorperative_id',
        'name',
    ];

    /**
     * Get the cooperative that owns the branch.
     */
    public function cooperative()
    {
        return $this->belongsTo(AcsCoorperative::class, 'acs_coorperative_id');
    }

    /**
     * Get the users for the cooperative branch.
     */
    public function users()
    {
        return $this->hasMany(AcsUser::class, 'acs_coorperative_branch_id');
    }
}
