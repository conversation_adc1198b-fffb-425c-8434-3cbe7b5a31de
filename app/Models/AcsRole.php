<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AcsRole extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'acs_roles';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get the users for the role.
     */
    public function users()
    {
        return $this->hasMany(AcsUser::class, 'acs_role_id');
    }
}
