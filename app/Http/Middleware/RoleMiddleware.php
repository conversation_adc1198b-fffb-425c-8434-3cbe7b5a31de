<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect('/login');
        }

        $user = Auth::user();

        // Check if user has a role
        if (!$user->role) {
            Auth::logout();
            return redirect('/login')->with('error', 'Your account does not have a valid role assigned.');
        }

        $userRoleId = $user->role->id;

        // Check if user has the required role
        if ($userRoleId != $role) {
            // Redirect to their appropriate dashboard instead of throwing 403
            $redirectUrl = $this->getUserDashboardUrl($userRoleId);
            return redirect($redirectUrl)->with('error', 'You do not have permission to access this area.');
        }

        return $next($request);
    }

    /**
     * Get the appropriate dashboard URL based on user role ID
     */
    private function getUserDashboardUrl(int $roleId): string
    {
        switch ($roleId) {
            case 1: // super_admin
                return url('/super-admin/dashboard');
            case 2: // admin
                return url('/admin/dashboard');
            case 3: // master_agent
                return url('/master-agent/dashboard');
            case 4: // agent
                return url('/agent/dashboard');
            default:
                return url('/login');
        }
    }
}
