<?php

namespace App\Http\Controllers;

use App\Services\AuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Show the login form
     */
    public function showLoginForm()
    {
        // If user is already authenticated, redirect to appropriate dashboard
        if ($this->authService->isAuthenticated()) {
            $user = $this->authService->getCurrentUser();
            if ($user && $user->role) {
                return redirect($this->getDashboardUrl($user->role->id));
            }
        }

        return view('auth.login');
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'email' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        // Attempt to login
        $result = $this->authService->login($request);

        if ($result['success']) {
            // Regenerate session ID for security (but keep session data)
            Session::regenerate();

            // Success message
            Session::flash('success', $result['message']);

            // Redirect to appropriate dashboard
            return redirect($result['redirect_url']);
        } else {
            // Error message
            Session::flash('error', $result['message']);
            return redirect()->back()->withInput();
        }
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request)
    {
        $result = $this->authService->logout();

        // Clear session data
        Session::flush();
        Session::regenerate();

        if ($result['success']) {
            Session::flash('success', $result['message']);
        } else {
            Session::flash('error', $result['message']);
        }

        return redirect('/login');
    }

    /**
     * Get dashboard URL based on role ID
     */
    private function getDashboardUrl($roleId)
    {
        switch ($roleId) {
            case 1: // super_admin
                return url('/super-admin/dashboard');
            case 2: // admin
                return url('/admin/dashboard');
            case 3: // master_agent
                return url('/master-agent/dashboard');
            case 4: // agent
                return url('/agent/dashboard');
            default:
                return url('/');
        }
    }
}
